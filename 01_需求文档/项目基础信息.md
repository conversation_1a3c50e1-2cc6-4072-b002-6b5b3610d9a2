# 项目基础信息

## 项目概述
- **项目名称**: translate (翻译服务)
- **项目类型**: 微服务应用
- **开发语言**: Java 17
- **框架版本**: Spring Boot 3.2.6

## 人员信息
- **开发人员**: AI-Code
- **项目负责人**: 待定
- **测试人员**: 待定

## 技术架构
- **架构模式**: 多模块Maven项目
- **微服务框架**: Spring Boot + Spring Cloud
- **服务注册**: Eureka
- **配置中心**: Apollo
- **数据库**: MySQL 8.0
- **ORM框架**: MyBatis 3.5.14
- **连接池**: HikariCP
- **缓存**: Redis (可选)
- **消息队列**: RocketMQ (可选)
- **任务调度**: XXL-Job
- **API文档**: OpenAPI 3.0 (SpringDoc)

## 模块结构
- **translate-common**: 公共工具类和共享组件
- **translate-dao**: 数据访问层
- **translate-facade**: 外部服务接口层
- **translate-service**: 业务服务层
- **translate-web**: Web控制层

## 环境信息
- **JDK版本**: OpenJDK 17.0.2
- **Maven版本**: 3.x
- **数据库**: MySQL 8.0 (10.187.2.60:3306)
- **服务注册中心**: http://merchant:<EMAIL>:8080/eureka/
- **配置中心**: http://apollo.akcrelease.com:8080
- **任务调度**: http://xxl-job-new.akcstable.com

## 功能概述
翻译服务系统，提供多语言翻译功能，支持：
- 文本翻译
- 批量翻译
- 翻译历史记录
- 多种翻译引擎支持

## 部署信息
- **部署方式**: Docker容器化部署
- **端口**: 8080
- **健康检查**: /slb/health
- **API文档**: /swagger-ui/index.html
