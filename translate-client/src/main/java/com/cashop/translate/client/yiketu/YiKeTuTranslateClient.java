package com.cashop.translate.client.yiketu;

import com.cashop.translate.common.dto.TranslateRequest;
import com.cashop.translate.common.dto.TranslateResponse;
import com.cashop.translate.common.enums.RequestStatusEnum;
import com.google.gson.Gson;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 易可图翻译客户端
 * 
 * <AUTHOR>
 */
@Component
public class YiKeTuTranslateClient {

    private static final Logger logger = LoggerFactory.getLogger(YiKeTuTranslateClient.class);

    @Value("${translate.yiketu.app-key:}")
    private String appKey;

    @Value("${translate.yiketu.app-secret:}")
    private String appSecret;

    @Value("${translate.yiketu.base-url:https://open-api.yiketu.com}")
    private String baseUrl;

    private final OkHttpClient httpClient;
    private final Gson gson = new Gson();

    public YiKeTuTranslateClient() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(15, TimeUnit.SECONDS)
                .writeTimeout(15, TimeUnit.SECONDS)
                .build();
    }

    /**
     * 检查客户端是否可用
     */
    public boolean isAvailable() {
        return StringUtils.hasText(appKey) && StringUtils.hasText(appSecret);
    }

    /**
     * 同步图片翻译
     */
    public CompletableFuture<TranslateResponse> translateImageSync(TranslateRequest request) {
        logger.info("开始调用易可图同步图片翻译，requestId: {}", request.getRequestId());
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                String url = baseUrl + "/gw/translate_img/translateImg";
                
                // 构建请求参数
                Map<String, Object> params = new TreeMap<>();
                params.put("timestamp", Long.toString(System.currentTimeMillis() / 1000));
                params.put("appKey", appKey);
                // 获取第一个图片URL（易可图只支持单张图片）
                String imageUrl = (request.getImageUrls() != null && !request.getImageUrls().isEmpty())
                    ? request.getImageUrls().get(0) : "";
                params.put("imgUrl", imageUrl);
                params.put("sourceLanguage", getYiKetuLanguageCode(request.getSourceLanguage()));
                params.put("targetLanguage", getYiKetuLanguageCode(request.getTargetLanguage()));
                params.put("isTranslateProductText", "0"); // 是否翻译商品主体上文字，默认0
                params.put("isTranslateBrandText", "0"); // 是否翻译品牌上文字，默认0

                // 生成签名
                params.put("sign", generateSign(params, appSecret));
                
                // 构建请求体
                RequestBody body = buildFormBody(params);
                Headers headers = new Headers.Builder()
                        .set("Content-Encoding", "UTF-8")
                        .build();
                
                Request httpRequest = new Request.Builder()
                        .url(url)
                        .headers(headers)
                        .post(body)
                        .build();

                // 发送请求
                try (Response response = httpClient.newCall(httpRequest).execute()) {
                    String responseStr = response.body().string();
                    logger.debug("易可图API响应: {}", responseStr);
                    
                    return parseResponse(request, responseStr);
                }
                
            } catch (Exception e) {
                logger.error("易可图同步图片翻译失败，requestId: {}", request.getRequestId(), e);
                return buildErrorResponse(request, "翻译请求失败: " + e.getMessage());
            }
        });
    }

    /**
     * 异步批量图片翻译 - 易可图不支持批量翻译
     */
    public CompletableFuture<TranslateResponse> translateImageBatch(TranslateRequest request) {
        logger.warn("易可图不支持批量翻译，requestId: {}", request.getRequestId());
        return CompletableFuture.completedFuture(
            buildErrorResponse(request, "易可图不支持批量翻译"));
    }

    /**
     * 获取批量翻译结果 - 易可图不支持批量翻译
     */
    public CompletableFuture<TranslateResponse> getBatchTranslateResult(String taskId) {
        logger.warn("易可图不支持批量翻译，taskId: {}", taskId);
        TranslateResponse response = new TranslateResponse();
        response.setSuccess(false);
        response.setErrorMessage("易可图不支持批量翻译");
        return CompletableFuture.completedFuture(response);
    }

    /**
     * 同步文本翻译 - 易可图不支持文本翻译
     */
    public CompletableFuture<TranslateResponse> translateTextSync(TranslateRequest request) {
        logger.warn("易可图不支持文本翻译，requestId: {}", request.getRequestId());
        return CompletableFuture.completedFuture(
            buildErrorResponse(request, "易可图不支持文本翻译"));
    }

    /**
     * 解析API响应
     */
    private TranslateResponse parseResponse(TranslateRequest request, String responseStr) {
        TranslateResponse result = new TranslateResponse();
        result.setRequestId(request.getRequestId());
        
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> responseMap = gson.fromJson(responseStr, Map.class);
            
            String resultStatus = (String) responseMap.get("result");
            Double code = (Double) responseMap.get("code");
            
            if ("success".equals(resultStatus) && code != null && code.intValue() == 200) {
                @SuppressWarnings("unchecked")
                Map<String, Object> data = (Map<String, Object>) responseMap.get("data");
                if (data != null) {
                    String translateImgUrl = (String) data.get("translateImgUrl");
                    
                    result.setSuccess(true);
                    result.setCloudApiStatus(RequestStatusEnum.SUCCESS.getCode());
                    result.setRequestStatus(RequestStatusEnum.SUCCESS.getCode());
                    result.setTranslateImageSyncResultUrl(translateImgUrl);
                    
                    logger.info("易可图翻译成功，requestId: {}, 翻译结果URL: {}", 
                            request.getRequestId(), translateImgUrl);
                } else {
                    result.setSuccess(false);
                    result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
                    result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
                    result.setErrorMessage("响应数据为空");
                }
            } else {
                String reason = (String) responseMap.get("reason");
                result.setSuccess(false);
                result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
                result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
                result.setErrorMessage("翻译失败: " + (reason != null ? reason : "未知错误"));
                
                logger.warn("易可图翻译失败，requestId: {}, 错误信息: {}", 
                        request.getRequestId(), reason);
            }
            
        } catch (Exception e) {
            logger.error("解析易可图响应失败，requestId: {}", request.getRequestId(), e);
            result.setSuccess(false);
            result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
            result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
            result.setErrorMessage("响应解析失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 构建错误响应
     */
    private TranslateResponse buildErrorResponse(TranslateRequest request, String errorMessage) {
        TranslateResponse result = new TranslateResponse();
        result.setRequestId(request.getRequestId());
        result.setSuccess(false);
        result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
        result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
        result.setErrorMessage(errorMessage);
        return result;
    }

    /**
     * 构建表单请求体
     */
    private RequestBody buildFormBody(Map<String, Object> params) {
        FormBody.Builder builder = new FormBody.Builder();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            builder.add(entry.getKey(), entry.getValue().toString());
        }
        return builder.build();
    }

    /**
     * 生成签名
     */
    private String generateSign(Map<String, Object> params, String appSecret) {
        // 移除sign字段（如果存在）
        Map<String, Object> filteredParams = new TreeMap<>();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (!"sign".equals(entry.getKey())) {
                filteredParams.put(entry.getKey(), entry.getValue());
            }
        }

        // 拼接参数名和参数值
        StringBuilder signString = new StringBuilder();
        signString.append(appSecret); // 头部拼接appSecret

        for (Map.Entry<String, Object> entry : filteredParams.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            String valueStr = value != null ? value.toString() : "";
            signString.append(key).append(valueStr);
        }

        signString.append(appSecret); // 尾部拼接appSecret

        // MD5加密并转大写
        return md5ToUpperCase(signString.toString());
    }

    /**
     * MD5加密并转为大写
     */
    private String md5ToUpperCase(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes(StandardCharsets.UTF_8));

            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString().toUpperCase();
        } catch (Exception e) {
            throw new RuntimeException("MD5加密失败", e);
        }
    }

    /**
     * 转换语言代码为易可图支持的格式
     */
    private String getYiKetuLanguageCode(String languageCode) {
        if (!StringUtils.hasText(languageCode)) {
            return "zh"; // 默认中文
        }
        
        // 易可图支持的语言代码映射
        switch (languageCode.toLowerCase()) {
            case "zh":
            case "zh-cn":
            case "chinese":
                return "zh";
            case "en":
            case "english":
                return "en";
            case "ja":
            case "japanese":
                return "ja";
            case "ko":
            case "korean":
                return "ko";
            case "es":
            case "spanish":
                return "es";
            case "fr":
            case "french":
                return "fr";
            case "pt":
            case "portuguese":
                return "pt";
            case "ru":
            case "russian":
                return "ru";
            case "de":
            case "german":
                return "de";
            case "it":
            case "italian":
                return "it";
            case "ar":
            case "arabic":
                return "ar";
            case "th":
            case "thai":
                return "th";
            case "vi":
            case "vietnamese":
                return "vi";
            case "id":
            case "indonesian":
                return "id";
            case "ms":
            case "malay":
                return "ms";
            case "tr":
            case "turkish":
                return "tr";
            case "nl":
            case "dutch":
                return "nl";
            case "pl":
            case "polish":
                return "pl";
            case "uk":
            case "ukrainian":
                return "uk";
            case "he":
            case "hebrew":
                return "he";
            case "zh-tw":
            case "zh-hant":
            case "traditional-chinese":
                return "zh-tw";
            default:
                logger.warn("不支持的语言代码: {}, 使用默认中文", languageCode);
                return "zh";
        }
    }
}
