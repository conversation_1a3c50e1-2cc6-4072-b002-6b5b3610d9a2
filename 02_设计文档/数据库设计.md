# 数据库设计文档

## 翻译请求记录表 (translate_request_record)

### 表结构设计

```sql
CREATE TABLE `translate_request_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_id` varchar(64) NOT NULL COMMENT '请求ID，用于去重和查询',
  `request_type` varchar(20) NOT NULL COMMENT '请求类型：SYNC_SINGLE(同步单张), ASYNC_BATCH(异步批量)',
  `provider` varchar(20) NOT NULL COMMENT '云服务提供商：ALIYUN, BAIDU, TENCENT, HUAWEI',
  `source_language` varchar(10) DEFAULT NULL COMMENT '源语言',
  `target_language` varchar(10) NOT NULL COMMENT '目标语言',
  `image_count` int(11) DEFAULT 1 COMMENT '图片数量',
  `request_params` text COMMENT '请求参数JSON',
  `request_status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '请求状态：PENDING(待处理), PROCESSING(处理中), SUCCESS(成功), FAILED(失败)',
  `cloud_api_status` varchar(20) DEFAULT NULL COMMENT '云服务API状态：SUCCESS(成功), FAILED(失败)',
  `cloud_api_response` text COMMENT '云服务API响应JSON',
  `cloud_api_async_status` varchar(20) DEFAULT NULL COMMENT '异步查询结果-云服务API状态：SUCCESS(成功), FAILED(失败), PROCESSING(处理中)',
  `cloud_api_async_response` text COMMENT '异步查询结果-云服务API响应JSON',
  `task_id` varchar(64) DEFAULT NULL COMMENT '阿里云批量翻译任务ID',
  `retry_count` int(11) DEFAULT 0 COMMENT '重试次数',
  `max_retry_count` int(11) DEFAULT 3 COMMENT '最大重试次数',
  `error_message` text COMMENT '错误信息',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_request_id` (`request_id`),
  KEY `idx_request_type` (`request_type`),
  KEY `idx_provider` (`provider`),
  KEY `idx_request_status` (`request_status`),
  KEY `idx_cloud_api_async_status` (`cloud_api_async_status`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='翻译请求记录表';
```

### 字段说明

#### 基础字段
- `id`: 主键ID，自增长
- `request_id`: 请求唯一标识，用于去重和结果查询
- `request_type`: 请求类型，区分同步单张和异步批量
- `provider`: 云服务提供商标识
- `source_language`: 源语言代码
- `target_language`: 目标语言代码
- `image_count`: 图片数量
- `request_params`: 完整的请求参数JSON

#### 状态字段
- `request_status`: 整体请求处理状态
  - `PENDING`: 待处理
  - `PROCESSING`: 处理中
  - `SUCCESS`: 成功
  - `FAILED`: 失败

- `cloud_api_status`: 云服务API调用状态
  - `SUCCESS`: 成功
  - `FAILED`: 失败

- `cloud_api_response`: 云服务API响应内容

- `cloud_api_async_status`: 异步查询结果状态（仅批量翻译）
  - `SUCCESS`: 成功
  - `FAILED`: 失败
  - `PROCESSING`: 处理中

- `cloud_api_async_response`: 异步查询结果响应内容

#### 任务管理字段
- `task_id`: 阿里云批量翻译任务ID
- `retry_count`: 当前重试次数
- `max_retry_count`: 最大重试次数
- `error_message`: 错误信息

#### 时间字段
- `created_time`: 创建时间
- `updated_time`: 更新时间

### 索引设计
- 主键索引：`id`
- 唯一索引：`request_id` (用于去重)
- 普通索引：
  - `request_type` (按类型查询)
  - `provider` (按提供商查询)
  - `request_status` (按状态查询)
  - `cloud_api_async_status` (定时任务查询)
  - `task_id` (阿里云任务查询)
  - `created_time` (按时间查询)

### 数据流转状态

#### 同步单张翻译流程
1. `PENDING` → `PROCESSING` → `SUCCESS/FAILED`
2. 只使用 `cloud_api_status` 和 `cloud_api_response`

#### 异步批量翻译流程
1. `PENDING` → `PROCESSING` → `SUCCESS/FAILED`
2. 先记录 `cloud_api_status` 和 `cloud_api_response`
3. 定时任务查询结果，更新 `cloud_api_async_status` 和 `cloud_api_async_response`
