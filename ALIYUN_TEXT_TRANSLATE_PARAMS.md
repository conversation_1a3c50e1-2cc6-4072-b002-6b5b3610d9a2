# 阿里云文本翻译参数传递机制

## 实现背景

阿里云的文本翻译请求参数（formatType、scene）应该由TranslateController传递下来，允许调用方指定，如果调用方未指定则使用默认值。这样可以提供更灵活的翻译配置。

## 实现内容

### ✅ 1. 扩展TranslateTextRequest DTO

**新增字段**:
```java
@Schema(description = "文本格式类型", example = "text", 
        allowableValues = {"text", "html"}, defaultValue = "text")
private String formatType = "text";

@Schema(description = "翻译场景", example = "general", 
        allowableValues = {"general", "title", "communication", "medical", "social"},
        defaultValue = "general")
private String scene = "general";
```

**特点**:
- 提供默认值，调用方可选择性指定
- 清晰的Swagger文档，包含所有可用值
- 类型安全的字符串参数

### ✅ 2. 扩展TranslateRequest 通用DTO

**新增字段**:
```java
/**
 * 文本格式类型（文本翻译时使用）
 */
private String formatType;

/**
 * 翻译场景（文本翻译时使用）
 */
private String scene;
```

**用途**: 在各层之间传递阿里云特有的翻译参数

### ✅ 3. 创建枚举定义

#### AliyunTranslateFormatType
```java
public enum AliyunTranslateFormatType {
    TEXT("text", "纯文本格式"),
    HTML("html", "HTML格式");
}
```

#### AliyunTranslateScene
```java
public enum AliyunTranslateScene {
    GENERAL("general", "通用场景"),
    TITLE("title", "标题场景"),
    COMMUNICATION("communication", "交流场景"),
    MEDICAL("medical", "医疗场景"),
    SOCIAL("social", "社交场景");
}
```

**优势**:
- 类型安全
- 集中管理可用值
- 提供验证方法

### ✅ 4. 参数验证器

**AliyunTranslateParamValidator**:
```java
public static String validateAndGetFormatType(String formatType);
public static String validateAndGetScene(String scene);
public static boolean isValidFormatType(String formatType);
public static boolean isValidScene(String scene);
```

**功能**:
- 验证参数有效性
- 提供默认值回退
- 统一的验证逻辑

### ✅ 5. 控制器层参数传递

**转换逻辑**:
```java
private TranslateRequest convertToTranslateRequest(TranslateTextRequest textRequest) {
    // ...
    // 设置阿里云文本翻译特有参数，验证并使用有效值或默认值
    translateRequest.setFormatType(AliyunTranslateParamValidator.validateAndGetFormatType(textRequest.getFormatType()));
    translateRequest.setScene(AliyunTranslateParamValidator.validateAndGetScene(textRequest.getScene()));
    // ...
}
```

### ✅ 6. 客户端层参数使用

**AliyunTranslateClient更新**:
```java
// 设置文本格式类型，如果未指定则使用默认值
String formatType = StringUtils.hasText(request.getFormatType()) ? request.getFormatType() : "text";
requestBuilder.formatType(formatType);

// 设置翻译场景，如果未指定则使用默认值
String scene = StringUtils.hasText(request.getScene()) ? request.getScene() : "general";
requestBuilder.scene(scene);
```

## 参数说明

### formatType - 文本格式类型

| 值 | 说明 | 使用场景 |
|---|------|----------|
| text | 纯文本格式 | 普通文本翻译 |
| html | HTML格式 | 包含HTML标签的文本翻译 |

### scene - 翻译场景

| 值 | 说明 | 使用场景 |
|---|------|----------|
| general | 通用场景 | 日常通用文本翻译 |
| title | 标题场景 | 标题、题目翻译 |
| communication | 交流场景 | 聊天、对话翻译 |
| medical | 医疗场景 | 医疗相关文本翻译 |
| social | 社交场景 | 社交媒体内容翻译 |

## 使用示例

### 1. 使用默认参数
```json
{
  "text": "Hello, world!",
  "targetLanguage": "zh"
}
```
**结果**: 使用默认的formatType="text"和scene="general"

### 2. 指定HTML格式
```json
{
  "text": "<p>Hello, <b>world</b>!</p>",
  "targetLanguage": "zh",
  "formatType": "html"
}
```

### 3. 指定医疗场景
```json
{
  "text": "Patient has fever and cough",
  "targetLanguage": "zh",
  "scene": "medical"
}
```

### 4. 完整参数指定
```json
{
  "requestId": "medical-001",
  "text": "<p>Patient diagnosis: <b>pneumonia</b></p>",
  "sourceLanguage": "en",
  "targetLanguage": "zh",
  "formatType": "html",
  "scene": "medical"
}
```

## 数据流向

```
调用方请求 → TranslateTextRequest → TranslateController → 
参数验证 → TranslateRequest → TranslateService → 
AliyunTranslateProvider → AliyunTranslateClient → 阿里云API
```

## 优势

### 1. 灵活性
- 调用方可根据需要指定参数
- 支持默认值，简化常用场景

### 2. 可维护性
- 集中的参数定义和验证
- 清晰的文档和示例

### 3. 扩展性
- 易于添加新的formatType或scene
- 统一的验证和处理机制

### 4. 类型安全
- 枚举定义防止无效值
- 编译时类型检查

## 后续优化

### 1. 动态参数支持
可以考虑从配置文件中读取支持的formatType和scene列表

### 2. 参数组合验证
某些formatType和scene的组合可能不被阿里云支持，可以添加组合验证

### 3. 缓存优化
对于相同参数的翻译请求，可以考虑添加缓存机制

## 总结

✅ **阿里云文本翻译参数传递机制实现完成**

1. 扩展了DTO支持formatType和scene参数
2. 创建了枚举和验证器确保参数有效性
3. 实现了从控制器到客户端的完整参数传递链
4. 提供了灵活的默认值机制
5. 保持了良好的类型安全性和可维护性

现在调用方可以灵活指定阿里云文本翻译的格式类型和场景，同时系统提供合理的默认值。
