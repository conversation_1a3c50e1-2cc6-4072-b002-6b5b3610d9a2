# 图片翻译请求参数DTO分离实现报告

## 实现背景

将TranslateController的图片同步翻译(/image/sync)和图片异步批量翻译(/image/batch)的请求参数分开，各自使用专门的DTO。图片同步翻译只处理单张图片，不需要使用list。

## 实现内容

### ✅ 1. 创建图片同步翻译专用DTO - TranslateImageSyncRequest

**文件位置**: `translate-web/src/main/java/com/cashop/translate/web/dto/TranslateImageSyncRequest.java`

**主要特点**:
```java
@Schema(description = "图片同步翻译请求")
public class TranslateImageSyncRequest {
    private String requestId;           // 请求ID（可选）
    private String sourceLanguage;      // 源语言（可选）
    @NotBlank private String targetLanguage;  // 目标语言（必填）
    private String imageUrl;            // 单张图片URL
    private String imageBase64;         // 单张图片Base64编码
    private String ext;                 // 扩展参数
}
```

**设计特点**:
- ✅ **单张图片处理**: 使用`String imageUrl`和`String imageBase64`，不使用List
- ✅ **二选一验证**: 图片URL和Base64编码只能选择其中一种
- ✅ **简化参数**: 专门针对同步单张图片翻译设计
- ✅ **清晰语义**: 字段名明确表示处理单张图片

### ✅ 2. 创建图片批量翻译专用DTO - TranslateImageBatchRequest

**文件位置**: `translate-web/src/main/java/com/cashop/translate/web/dto/TranslateImageBatchRequest.java`

**主要特点**:
```java
@Schema(description = "图片批量翻译请求")
public class TranslateImageBatchRequest {
    private String requestId;                    // 请求ID（可选）
    private String sourceLanguage;               // 源语言（可选）
    @NotBlank private String targetLanguage;    // 目标语言（必填）
    @NotEmpty private List<String> imageUrls;   // 图片URL列表（必填）
    private List<String> imageBase64List;       // 图片Base64编码列表
    private String scene = "general";           // 图片翻译场景
    private String ext;                         // 扩展参数
}
```

**设计特点**:
- ✅ **批量处理**: 使用`List<String>`处理多张图片
- ✅ **场景支持**: 包含`scene`字段，支持图片翻译场景选择
- ✅ **数量限制**: 支持批量翻译的数量控制（最多20张）
- ✅ **验证完整**: 包含`@NotEmpty`验证确保至少有一张图片

### ✅ 3. 更新TranslateController

#### 3.1 图片同步翻译方法更新
```java
@PostMapping("/image/sync")
public CompletableFuture<? extends ApiResponse<?>> translateImageSync(
        @Valid @RequestBody TranslateImageSyncRequest request) {
    
    // 参数校验
    if (request.getImageUrl() == null && request.getImageBase64() == null) {
        return CompletableFuture.completedFuture(ApiResponse.error(400, "图片URL或Base64编码不能为空"));
    }
    
    if (request.getImageUrl() != null && request.getImageBase64() != null) {
        return CompletableFuture.completedFuture(ApiResponse.error(400, "图片URL和Base64编码只能选择其中一种"));
    }
    
    // 转换请求对象
    TranslateRequest translateRequest = convertToTranslateRequest(request);
    // ...
}
```

#### 3.2 图片批量翻译方法更新
```java
@PostMapping("/image/batch")
public CompletableFuture<? extends ApiResponse<?>> translateImageBatch(
        @Valid @RequestBody TranslateImageBatchRequest request) {
    
    // 转换请求对象
    TranslateRequest translateRequest = convertToTranslateRequest(request);
    // ...
}
```

### ✅ 4. 专门的转换方法

#### 4.1 同步翻译转换方法
```java
private TranslateRequest convertToTranslateRequest(TranslateImageSyncRequest imageSyncRequest) {
    TranslateRequest translateRequest = new TranslateRequest();
    translateRequest.setRequestId(imageSyncRequest.getRequestId());
    translateRequest.setSourceLanguage(imageSyncRequest.getSourceLanguage());
    translateRequest.setTargetLanguage(imageSyncRequest.getTargetLanguage());
    
    // 处理单张图片 - 转换为List格式以兼容底层服务
    if (imageSyncRequest.getImageUrl() != null) {
        translateRequest.setImageUrls(Arrays.asList(imageSyncRequest.getImageUrl()));
    }
    if (imageSyncRequest.getImageBase64() != null) {
        translateRequest.setImageBase64List(Arrays.asList(imageSyncRequest.getImageBase64()));
    }
    
    translateRequest.setExt(imageSyncRequest.getExt());
    return translateRequest;
}
```

#### 4.2 批量翻译转换方法
```java
private TranslateRequest convertToTranslateRequest(TranslateImageBatchRequest imageBatchRequest) {
    TranslateRequest translateRequest = new TranslateRequest();
    translateRequest.setRequestId(imageBatchRequest.getRequestId());
    translateRequest.setSourceLanguage(imageBatchRequest.getSourceLanguage());
    translateRequest.setTargetLanguage(imageBatchRequest.getTargetLanguage());
    translateRequest.setImageUrls(imageBatchRequest.getImageUrls());
    translateRequest.setImageBase64List(imageBatchRequest.getImageBase64List());
    
    // 设置图片翻译场景
    String imageScene = imageBatchRequest.getScene() != null ? imageBatchRequest.getScene() : "general";
    translateRequest.setImageScene(imageScene);
    
    translateRequest.setExt(imageBatchRequest.getExt());
    return translateRequest;
}
```

## 对比分析

### 修改前 vs 修改后

| 方面 | 修改前 | 修改后 |
|------|--------|--------|
| DTO数量 | 1个通用DTO | 2个专用DTO |
| 同步翻译图片参数 | `List<String> imageUrls` | `String imageUrl` |
| 批量翻译图片参数 | `List<String> imageUrls` | `List<String> imageUrls` |
| 参数验证 | 通用验证逻辑 | 专门的验证逻辑 |
| API语义 | 模糊（同步也用List） | 清晰（同步用单个，批量用List） |

### 优势对比

| 优势 | 同步翻译 | 批量翻译 |
|------|----------|----------|
| **类型安全** | ✅ 编译时确保单张图片 | ✅ 编译时确保批量处理 |
| **参数清晰** | ✅ 单个字段，语义明确 | ✅ List字段，支持批量 |
| **验证精确** | ✅ 二选一验证 | ✅ 非空列表验证 |
| **文档清晰** | ✅ Swagger文档明确 | ✅ 包含场景参数说明 |

## 使用示例

### 图片同步翻译请求
```json
{
  "requestId": "sync-001",
  "targetLanguage": "zh",
  "imageUrl": "https://example.com/image.jpg"
}
```

或者使用Base64：
```json
{
  "requestId": "sync-002", 
  "targetLanguage": "zh",
  "imageBase64": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
}
```

### 图片批量翻译请求
```json
{
  "requestId": "batch-001",
  "targetLanguage": "zh",
  "scene": "e-commerce",
  "imageUrls": [
    "https://example.com/image1.jpg",
    "https://example.com/image2.jpg",
    "https://example.com/image3.jpg"
  ]
}
```

## API接口变更

### 接口路径保持不变
- `POST /api/translate/image/sync` - 图片同步翻译
- `POST /api/translate/image/batch` - 图片批量翻译

### 请求参数变更
- **同步翻译**: 使用`TranslateImageSyncRequest`，单张图片参数
- **批量翻译**: 使用`TranslateImageBatchRequest`，批量图片参数

### 响应格式保持不变
- 同步翻译：立即返回翻译结果
- 批量翻译：返回任务ID，需要通过结果查询接口获取结果

## 向后兼容性

### 兼容性说明
- ✅ **接口路径**: 完全兼容，路径未变更
- ⚠️ **请求参数**: 不兼容，需要调整客户端代码
- ✅ **响应格式**: 完全兼容，响应结构未变更

### 迁移指南
1. **同步翻译客户端**：将`imageUrls[0]`改为`imageUrl`
2. **批量翻译客户端**：保持`imageUrls`不变，可选择添加`scene`参数
3. **测试验证**：使用新的请求格式测试接口功能

## 总结

✅ **图片翻译请求参数DTO分离完成**

1. **创建专用DTO**：TranslateImageSyncRequest和TranslateImageBatchRequest
2. **优化参数设计**：同步翻译使用单个字段，批量翻译使用List字段
3. **增强类型安全**：编译时确保参数类型正确
4. **改进API语义**：接口参数更加清晰和直观
5. **保持功能完整**：所有翻译功能保持不变

现在图片同步翻译和批量翻译拥有了各自专门的DTO，API设计更加清晰和类型安全。
