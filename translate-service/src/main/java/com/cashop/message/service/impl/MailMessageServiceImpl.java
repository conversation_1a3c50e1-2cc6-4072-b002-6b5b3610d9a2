
package com.cashop.message.service.impl;

import java.time.Year;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cashop.message.common.dto.MessageResponse;
import com.sun.mail.util.MailSSLSocketFactory;

import jakarta.mail.Transport;
import jakarta.mail.Address;
import jakarta.mail.Authenticator;
import jakarta.mail.Message;
import jakarta.mail.Multipart;
import jakarta.mail.PasswordAuthentication;
import jakarta.mail.Session;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeBodyPart;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeMultipart;

public class MailMessageServiceImpl {

    static Logger logger = LoggerFactory.getLogger(MailMessageServiceImpl.class);

    public static void main(String[] args) {
        
        String title = "notify validate code";
        String content = "validate code: " + randomInt(100000, 999999) + ", date: " + new Date();

        // 加载模板
        String template = "<!DOCTYPE html>" +
                "<html>" +
                "<head>" +
                "    <meta charset=\"UTF-8\">" +
                "    <title>${title}</title>" +
                "</head>" +
                "<body>" +
                "    <h1>${title}</h1>" +
                "    <p>${content}</p>" +
                "    <p>© ${year} Cashop. All rights reserved.</p>" +
                "</body>" +
                "</html>";

        // 准备变量
        Map<String, Object> variables = new HashMap<>();
        variables.put("title", title);
        variables.put("content", content);
        variables.put("year", String.valueOf(Year.now().getValue()));

        // 处理模板
        String htmlContent = processTemplate(template, variables);
        System.out.println(htmlContent);
        
        try {
            sendEmail(
                "smtp.exmail.qq.com", 
                "465", 
                "<EMAIL>", 
                "Okmijn456123", 
                Arrays.asList("<EMAIL>"), 
                title, 
                htmlContent
            );
            System.out.println("发送成功");
        } catch (Exception e) {
            System.out.println("发送成功");
            e.printStackTrace();
        }

    }

    private static String randomInt(int i, int j) {
        return String.valueOf((int)(Math.random()*(j-i+1)+i));
    }

    public static MessageResponse sendEmail(String host, String port, String fromEmail, String fromEmailPwd, List<String> toEmailList, String subject, String htmlContent) throws Exception {
        
        try {
            // 发送邮件配置
            Properties props = new Properties();
            // 协议
            props.put("mail.transport.protocol", "smtp");
            props.put("mail.smtp.host", host);
            props.put("mail.smtp.port", port);
            props.put("mail.smtp.auth", "true");
            //props.put("mail.smtp.starttls.enable", "true");// 启用TLS加密
            // 使用SSL，企业邮箱必需！
            MailSSLSocketFactory mailSSLSocketFactory = new MailSSLSocketFactory();
            mailSSLSocketFactory.setTrustAllHosts(true);
            props.put("mail.smtp.ssl.socketFactory", mailSSLSocketFactory);
            props.put("mail.smtp.ssl.enable", "true");

            Session session = Session.getInstance(props, new Authenticator() {

                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(fromEmail, fromEmailPwd);
                }
            });

            Message message = new MimeMessage(session);
            message.setFrom(new InternetAddress(fromEmail));
            //message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(toEmail));
            // 添加所有收件人
            Address[] addresses = new Address[toEmailList.size()];
            for (int i = 0; i < toEmailList.size(); i++) {
                addresses[i] = new InternetAddress(toEmailList.get(i));
            }
            message.setRecipients(Message.RecipientType.TO, addresses);
            message.setSubject(subject);
            message.setSentDate(new Date());
            // 创建HTML内容
            MimeBodyPart htmlPart = new MimeBodyPart();
            htmlPart.setContent(htmlContent, "text/html; charset=utf-8");

            // 创建Multipart
            Multipart multipart = new MimeMultipart();
            multipart.addBodyPart(htmlPart);

            message.setContent(multipart);
            message.saveChanges();
            Transport.send(message);
            return MessageResponse.buildSuccessResponse("发送邮件成功", null);
        } catch (Exception e) {
            logger.err
            return MessageResponse.buildErrorResponse("发送邮件失败: " + e.getMessage());
        }
        
    }

    private static String processTemplate(String template, Map<String, Object> variables) {
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String entryValue = "";
            if (entry.getValue() != null) {
                entryValue = entry.getValue().toString();
            }
            template = template.replaceAll("\\$\\{" + entry.getKey() + "\\}", entryValue);
        }
        return template;
    }

}



