package com.cashop.translate.service.job;

import com.cashop.translate.service.config.ProviderRouteConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 本地定时任务配置
 * 
 * <AUTHOR>
 */
@Configuration
@EnableScheduling
@ConditionalOnProperty(name = "translate.image.batch.result.type", havingValue = "local-job", matchIfMissing = true)
public class LocalScheduleConfig {

    private static final Logger logger = LoggerFactory.getLogger(LocalScheduleConfig.class);

    @Autowired
    private BatchTranslateJobService batchTranslateJobService;

    @Autowired
    private ProviderRouteConfig routeConfig;

    /**
     * 批量翻译结果获取任务
     * 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000)
    public void batchTranslateResultJob() {
        if (!"local-job".equals(routeConfig.getBatchResultType())) {
            return;
        }
        
        try {
            batchTranslateJobService.batchTranslateResultJob();
        } catch (Exception e) {
            logger.error("本地批量翻译结果获取任务执行异常", e);
        }
    }

    /**
     * 清理超时任务
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000)
    public void cleanTimeoutTasksJob() {
        if (!"local-job".equals(routeConfig.getBatchResultType())) {
            return;
        }
        
        try {
            batchTranslateJobService.cleanTimeoutTasksJob();
        } catch (Exception e) {
            logger.error("本地清理超时任务执行异常", e);
        }
    }
}
