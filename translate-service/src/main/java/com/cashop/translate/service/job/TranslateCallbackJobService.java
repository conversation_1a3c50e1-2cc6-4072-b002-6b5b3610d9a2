package com.cashop.translate.service.job;

import com.cashop.translate.common.enums.CallbackStatusEnum;
import com.cashop.translate.dao.entity.TranslateRequestRecord;
import com.cashop.translate.dao.mapper.TranslateRequestRecordMapper;
import com.google.gson.Gson;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 翻译结果回调定时任务服务
 * 
 * <AUTHOR>
 */
@Service
public class TranslateCallbackJobService {

    private static final Logger logger = LoggerFactory.getLogger(TranslateCallbackJobService.class);

    @Autowired
    private TranslateRequestRecordMapper translateRequestRecordMapper;

    private final OkHttpClient httpClient;
    private final Gson gson = new Gson();
    private final AtomicBoolean isRunning = new AtomicBoolean(false);

    public TranslateCallbackJobService() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
    }

    /**
     * 执行翻译结果回调任务
     */
    public void executeCallbackJob() {
        if (!isRunning.compareAndSet(false, true)) {
            logger.warn("翻译结果回调任务正在执行中，跳过本次执行");
            return;
        }

        try {
            logger.info("开始执行翻译结果回调任务");

            // 查询需要回调的记录
            List<TranslateRequestRecord> pendingRecords = translateRequestRecordMapper.selectPendingCallbackRecords(
                    CallbackStatusEnum.INITIAL.getCode(), 3);

            if (CollectionUtils.isEmpty(pendingRecords)) {
                logger.info("没有需要回调的记录");
                return;
            }

            logger.info("找到{}条需要回调的记录", pendingRecords.size());

            // 处理每条记录
            for (TranslateRequestRecord record : pendingRecords) {
                try {
                    processCallbackRecord(record);
                } catch (Exception e) {
                    logger.error("处理回调记录失败，recordId: {}", record.getId(), e);
                }
            }

            logger.info("翻译结果回调任务执行完成");

        } catch (Exception e) {
            logger.error("执行翻译结果回调任务失败", e);
        } finally {
            isRunning.set(false);
        }
    }

    /**
     * 处理单条回调记录
     */
    private void processCallbackRecord(TranslateRequestRecord record) {
        logger.info("开始处理回调记录，recordId: {}, requestId: {}, callback: {}", 
                record.getId(), record.getRequestId(), record.getCallback());

        try {
            // 构建回调数据
            Map<String, Object> callbackData = buildCallbackData(record);
            
            // 发送回调请求
            String response = sendCallbackRequest(record.getCallback(), callbackData);
            
            // 更新回调状态为成功
            updateCallbackStatus(record, CallbackStatusEnum.SUCCESS, response);
            
            logger.info("回调成功，recordId: {}, requestId: {}", record.getId(), record.getRequestId());
            
        } catch (Exception e) {
            logger.error("回调失败，recordId: {}, requestId: {}", record.getId(), record.getRequestId(), e);
            
            // 更新回调状态为失败
            updateCallbackStatus(record, CallbackStatusEnum.FAILED, "回调失败: " + e.getMessage());
        }
    }

    /**
     * 构建回调数据
     */
    private Map<String, Object> buildCallbackData(TranslateRequestRecord record) {
        Map<String, Object> data = new HashMap<>();
        
        // 基本信息
        data.put("requestId", record.getRequestId());
        data.put("requestType", record.getRequestType());
        data.put("provider", record.getProvider());
        data.put("sourceLanguage", record.getSourceLanguage());
        data.put("targetLanguage", record.getTargetLanguage());
        data.put("requestStatus", record.getRequestStatus());
        
        // 翻译结果
        if (StringUtils.hasText(record.getImageSyncResultUrl())) {
            data.put("translateImageSyncResultUrl", record.getImageSyncResultUrl());
        }
        
        if (StringUtils.hasText(record.getImageBatchResults())) {
            data.put("imageBatchResults", record.getImageBatchResults());
        }
        
        if (StringUtils.hasText(record.getTranslatedText())) {
            data.put("translatedText", record.getTranslatedText());
        }
        
        // 错误信息（如果有）
        if (StringUtils.hasText(record.getErrorMessage())) {
            data.put("errorMessage", record.getErrorMessage());
        }
        
        // 时间信息
        data.put("createdTime", record.getCreatedTime());
        data.put("updatedTime", record.getUpdatedTime());
        
        return data;
    }

    /**
     * 发送回调请求
     */
    private String sendCallbackRequest(String callbackUrl, Map<String, Object> data) throws Exception {
        String jsonData = gson.toJson(data);
        
        RequestBody body = RequestBody.create(jsonData.getBytes(), MediaType.parse("application/json; charset=utf-8"));
        
        Request request = new Request.Builder()
                .url(callbackUrl)
                .post(body)
                .addHeader("Content-Type", "application/json")
                .addHeader("User-Agent", "TranslateService/1.0")
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "";
            
            if (!response.isSuccessful()) {
                throw new RuntimeException("HTTP请求失败，状态码: " + response.code() + ", 响应: " + responseBody);
            }
            
            return responseBody;
        }
    }

    /**
     * 更新回调状态
     */
    private void updateCallbackStatus(TranslateRequestRecord record, CallbackStatusEnum status, String response) {
        try {
            // 计算重试次数
            Integer retryCount = record.getCallbackRetryCount();
            if (retryCount == null) {
                retryCount = 0;
            }
            retryCount++;
            
            // 限制响应长度
            String limitedResponse = response;
            if (response != null && response.length() > 2000) {
                limitedResponse = response.substring(0, 2000) + "...";
            }
            
            int updateCount = translateRequestRecordMapper.updateCallbackStatus(
                    record.getId(), 
                    status.getCode(), 
                    limitedResponse, 
                    retryCount);
            
            if (updateCount > 0) {
                logger.info("更新回调状态成功，recordId: {}, status: {}, retryCount: {}", 
                        record.getId(), status.getDescription(), retryCount);
            } else {
                logger.warn("更新回调状态失败，recordId: {}", record.getId());
            }
            
        } catch (Exception e) {
            logger.error("更新回调状态异常，recordId: {}", record.getId(), e);
        }
    }

    /**
     * 检查任务是否正在运行
     */
    public boolean isRunning() {
        return isRunning.get();
    }
}
