package com.cashop.translate.service.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 云服务提供商路由配置
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "translate")
public class ProviderRouteConfig {

    /**
     * 同步翻译提供商权重配置
     */
    private Map<String, Integer> syncProviderWeight = new HashMap<>();

    /**
     * 异步翻译提供商权重配置
     */
    private Map<String, Integer> asyncProviderWeight = new HashMap<>();

    /**
     * 文本翻译提供商权重配置
     */
    private Map<String, Integer> textProviderWeight = new HashMap<>();

    /**
     * 批量翻译结果获取类型：local-job 或 xxl-job
     */
    private String batchResultType = "local-job";

    public Map<String, Integer> getSyncProviderWeight() {
        return syncProviderWeight;
    }

    public void setSyncProviderWeight(Map<String, Integer> syncProviderWeight) {
        this.syncProviderWeight = syncProviderWeight;
    }

    public Map<String, Integer> getAsyncProviderWeight() {
        return asyncProviderWeight;
    }

    public void setAsyncProviderWeight(Map<String, Integer> asyncProviderWeight) {
        this.asyncProviderWeight = asyncProviderWeight;
    }

    public Map<String, Integer> getTextProviderWeight() {
        return textProviderWeight;
    }

    public void setTextProviderWeight(Map<String, Integer> textProviderWeight) {
        this.textProviderWeight = textProviderWeight;
    }

    public String getBatchResultType() {
        return batchResultType;
    }

    public void setBatchResultType(String batchResultType) {
        this.batchResultType = batchResultType;
    }
}
