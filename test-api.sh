#!/bin/bash

# 翻译服务API测试脚本
# 用于验证文本同步翻译、图片同步翻译、图片批量异步翻译接口

BASE_URL="http://localhost:8080/api"

echo "=== 翻译服务API测试 ==="
echo "基础URL: $BASE_URL"
echo ""

# 1. 测试文本同步翻译
echo "1. 测试文本同步翻译接口"
echo "POST $BASE_URL/translate/text/sync"
curl -X POST "$BASE_URL/translate/text/sync" \
  -H "Content-Type: application/json" \
  -d '{
    "requestId": "test-text-001",
    "text": "Hello, world!",
    "sourceLanguage": "en",
    "targetLanguage": "zh"
  }' | jq '.'
echo ""
echo ""

# 2. 测试图片同步翻译
echo "2. 测试图片同步翻译接口"
echo "POST $BASE_URL/translate/image/sync"
curl -X POST "$BASE_URL/translate/image/sync" \
  -H "Content-Type: application/json" \
  -d '{
    "requestId": "test-image-sync-001",
    "imageUrls": ["https://example.com/test-image.jpg"],
    "sourceLanguage": "en",
    "targetLanguage": "zh"
  }' | jq '.'
echo ""
echo ""

# 3. 测试图片批量异步翻译
echo "3. 测试图片批量异步翻译接口"
echo "POST $BASE_URL/translate/image/batch"
curl -X POST "$BASE_URL/translate/image/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "requestId": "test-image-batch-001",
    "imageUrls": [
      "https://example.com/test-image1.jpg",
      "https://example.com/test-image2.jpg"
    ],
    "sourceLanguage": "en",
    "targetLanguage": "zh"
  }' | jq '.'
echo ""
echo ""

# 4. 测试获取批量翻译结果
echo "4. 测试获取批量翻译结果接口"
echo "GET $BASE_URL/translate/image/batch/result?requestId=test-batch-result-001"
curl -X GET "$BASE_URL/translate/image/batch/result?requestId=test-batch-result-001" | jq '.'
echo ""
echo ""

# 5. 测试阿里云服务可用性
echo "5. 测试阿里云服务可用性"
echo "GET $BASE_URL/test/aliyun/available"
curl -X GET "$BASE_URL/test/aliyun/available" | jq '.'
echo ""
echo ""

# 6. 测试阿里云文本翻译
echo "6. 测试阿里云文本翻译"
echo "GET $BASE_URL/test/aliyun/text?text=Hello&targetLanguage=zh&sourceLanguage=en"
curl -X GET "$BASE_URL/test/aliyun/text?text=Hello&targetLanguage=zh&sourceLanguage=en" | jq '.'
echo ""
echo ""

echo "=== 测试完成 ==="
