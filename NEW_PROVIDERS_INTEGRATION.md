# 新图片翻译供应商接入文档

本文档描述了易可图（YiKeTu）和鬼手剪辑（GhostCut）两家图片翻译供应商的接入情况。

## 概述

### 易可图（YiKeTu）
- **供应商代码**: `YIKETU`
- **支持功能**: 单张图片同步翻译
- **不支持功能**: 批量翻译、文本翻译
- **API特点**: 同步返回翻译结果

### 鬼手剪辑（GhostCut）
- **供应商代码**: `GHOSTCUT`
- **支持功能**: 单张图片异步翻译
- **不支持功能**: 批量翻译、文本翻译
- **API特点**: 异步处理，需要分两步（提交任务 + 查询结果）

## 架构设计

### 1. 枚举扩展
在 `ProviderEnum` 中添加了两个新的供应商：
```java
/**
 * 易可图
 */
YIKETU("YIKETU", "易可图"),

/**
 * 鬼手剪辑
 */
GHOSTCUT("GHOSTCUT", "鬼手剪辑");
```

### 2. 客户端实现
- `YiKeTuTranslateClient`: 易可图翻译客户端
- `GhostCutTranslateClient`: 鬼手剪辑翻译客户端

### 3. 提供商实现
- `YiKeTuTranslateProvider`: 易可图翻译提供商
- `GhostCutTranslateProvider`: 鬼手剪辑翻译提供商

## 配置说明

### 应用配置
在 `application-local.yml` 中添加配置：

```yaml
translate:
  # 易可图配置
  yiketu:
    app-key: **********
    app-secret: db8870ca84e32b7ea0bf10a61d2eaa01
    base-url: https://open-api.yiketu.com

  # 鬼手剪辑配置
  ghostcut:
    app-key: e8d732890c194fee8a167ccb23d46d10
    app-secret: e50b179761c6454ab91add516a124c07
    base-url: https://api.zhaoli.com

  # 同步翻译提供商权重配置
  sync-provider-weight:
    aliyun: 50
    yiketu: 30
    ghostcut: 20

  # 异步翻译提供商权重配置
  async-provider-weight:
    aliyun: 70
    ghostcut: 30
```

### 权重配置说明
- **同步翻译**: 阿里云50%，易可图30%，鬼手剪辑20%
- **异步翻译**: 阿里云70%，鬼手剪辑30%（易可图不支持异步）
- **文本翻译**: 仅阿里云100%（其他供应商不支持）

## API接口

### 易可图API
- **接口地址**: `https://open-api.yiketu.com/gw/translate_img/translateImg`
- **请求方式**: POST (Form)
- **认证方式**: AppKey + 签名
- **响应方式**: 同步返回翻译结果

### 鬼手剪辑API
- **提交任务**: `https://api.zhaoli.com/v-w-c/gateway/ve/image/translate`
- **查询结果**: `https://api.zhaoli.com/v-w-c/gateway/ve/image/translate/query`
- **请求方式**: POST (JSON)
- **认证方式**: AppKey + AppSign
- **响应方式**: 异步处理

## 批量翻译结果获取任务兼容性

### 鬼手剪辑异步处理
鬼手剪辑的异步翻译通过现有的 `BatchTranslateJobService` 进行处理：

1. **任务提交**: 调用 `translateImageBatch` 提交翻译任务
2. **状态跟踪**: 任务状态保存到数据库
3. **结果获取**: 定时任务调用 `getBatchTranslateResult` 查询结果
4. **状态更新**: 根据查询结果更新数据库状态

### 状态映射
- **status = -1**: `PROCESSING` (待处理)
- **status = 1**: `SUCCESS` (处理成功)
- **status > 1**: `FAILED` (处理失败)

## 语言代码映射

### 易可图支持的语言
- 中文: `zh`
- 英语: `en`
- 日语: `ja`
- 韩语: `ko`
- 西班牙语: `es`
- 法语: `fr`
- 葡萄牙语: `pt`
- 俄语: `ru`
- 德语: `de`
- 意大利语: `it`
- 阿拉伯语: `ar`
- 泰语: `th`
- 越南语: `vi`
- 印尼语: `id`
- 马来语: `ms`
- 土耳其语: `tr`
- 荷兰语: `nl`
- 波兰语: `pl`
- 乌克兰语: `uk`
- 希伯来语: `he`
- 繁体中文: `zh-tw`

### 鬼手剪辑支持的语言
- 中文: `zh`
- 繁体中文: `zh-hant`
- 英语: `en`
- 日语: `ja`
- 韩语: `ko`
- 泰语: `th`
- 越南语: `vi`
- 印尼语: `id`
- 马来语: `ms`
- 印地语: `hi`
- 俄语: `ru`
- 德语: `de`
- 法语: `fr`
- 阿拉伯语: `ar`
- 西班牙语: `es`
- 葡萄牙语: `pt`
- 意大利语: `it`
- 波兰语: `pl`
- 自动识别: `auto`

## 测试

### 集成测试
运行 `NewProvidersIntegrationTest` 来验证集成：

```bash
mvn test -Dtest=NewProvidersIntegrationTest
```

### 测试内容
1. **提供商可用性测试**: 验证新提供商是否正确注册
2. **易可图图片翻译测试**: 测试同步图片翻译功能
3. **鬼手剪辑图片翻译测试**: 测试异步图片翻译功能
4. **不支持操作测试**: 验证文本翻译等不支持的操作

## 注意事项

### 易可图
1. 仅支持同步图片翻译
2. 图片要求：不超过 4000*4000px，大小不超过 10MB
3. 支持格式：png、jpeg、jpg、bmp、webp
4. 需要配置正确的 AppKey 和 AppSecret

### 鬼手剪辑
1. 支持异步图片翻译
2. 图片要求：不超过 2000*2000px，大小不超过 50MB
3. 支持格式：png、jpeg、jpg、bmp、webp
4. URL地址中不能包含中文字符
5. 需要配置正确的 AppKey 和 AppSecret

### 通用注意事项
1. 两个供应商都不支持批量翻译
2. 两个供应商都不支持文本翻译
3. 配置文件中的密钥信息应该通过配置中心管理
4. 生产环境需要更新相应的配置

## 监控和日志

### 日志级别
- INFO: 正常的翻译请求和响应
- WARN: 不支持的操作或配置问题
- ERROR: 翻译失败或异常情况

### 关键日志
- 提供商选择日志
- 翻译请求和响应日志
- 异步任务状态变更日志
- 错误和异常日志

## 扩展性

当前架构支持轻松添加新的翻译供应商：

1. 在 `ProviderEnum` 中添加新的枚举值
2. 实现对应的 `Client` 类
3. 实现对应的 `Provider` 类
4. 添加配置信息
5. 更新权重配置

系统会自动识别和使用新的供应商。
