package com.cashop.translate.web.controller;

import com.cashop.translate.common.dto.TranslateRequest;
import com.cashop.translate.common.dto.TranslateResponse;
import com.cashop.translate.common.enums.ProviderEnum;
import com.cashop.translate.service.provider.CloudTranslateProvider;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 新供应商集成测试
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("local")
public class NewProvidersIntegrationTest {

    private static final Logger logger = LoggerFactory.getLogger(NewProvidersIntegrationTest.class);

    @Autowired
    private List<CloudTranslateProvider> translateProviders;

    @Test
    public void testProviderAvailability() {
        logger.info("开始测试翻译提供商可用性");
        
        for (CloudTranslateProvider provider : translateProviders) {
            ProviderEnum providerType = provider.getProviderType();
            boolean isAvailable = provider.isAvailable();
            
            logger.info("提供商: {}, 可用性: {}", providerType.getDescription(), isAvailable);
            
            // 验证新添加的提供商
            if (providerType == ProviderEnum.YIKETU) {
                logger.info("易可图提供商已成功注册");
            } else if (providerType == ProviderEnum.GHOSTCUT) {
                logger.info("鬼手剪辑提供商已成功注册");
            }
        }
    }

    @Test
    public void testYiKeTuImageTranslation() {
        logger.info("开始测试易可图图片翻译");
        
        CloudTranslateProvider yiketuProvider = getProviderByType(ProviderEnum.YIKETU);
        if (yiketuProvider == null) {
            logger.warn("易可图提供商未找到，跳过测试");
            return;
        }
        
        if (!yiketuProvider.isAvailable()) {
            logger.warn("易可图提供商不可用，跳过测试");
            return;
        }

        TranslateRequest request = new TranslateRequest();
        request.setRequestId(UUID.randomUUID().toString());
        request.setImageUrls(List.of("https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/10.jpg"));
        request.setSourceLanguage("zh");
        request.setTargetLanguage("en");

        try {
            CompletableFuture<TranslateResponse> future = yiketuProvider.translateImageSync(request);
            TranslateResponse response = future.get();
            
            logger.info("易可图翻译结果 - 成功: {}, 错误信息: {}, 结果URL: {}", 
                    response.isSuccess(), response.getErrorMessage(), response.getTranslateImageSyncResultUrl());
        } catch (Exception e) {
            logger.error("易可图翻译测试失败", e);
        }
    }

    @Test
    public void testGhostCutImageTranslation() {
        logger.info("开始测试鬼手剪辑图片翻译");
        
        CloudTranslateProvider ghostcutProvider = getProviderByType(ProviderEnum.GHOSTCUT);
        if (ghostcutProvider == null) {
            logger.warn("鬼手剪辑提供商未找到，跳过测试");
            return;
        }
        
        if (!ghostcutProvider.isAvailable()) {
            logger.warn("鬼手剪辑提供商不可用，跳过测试");
            return;
        }

        TranslateRequest request = new TranslateRequest();
        request.setRequestId(UUID.randomUUID().toString());
        request.setImageUrls(List.of("https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/10.jpg"));
        request.setSourceLanguage("zh");
        request.setTargetLanguage("en");

        try {
            // 测试异步翻译（提交任务）
            CompletableFuture<TranslateResponse> submitFuture = ghostcutProvider.translateImageBatch(request);
            TranslateResponse submitResponse = submitFuture.get();
            
            logger.info("鬼手剪辑任务提交结果 - 成功: {}, 任务ID: {}, 错误信息: {}", 
                    submitResponse.isSuccess(), submitResponse.getTaskId(), submitResponse.getErrorMessage());
            
            if (submitResponse.isSuccess() && submitResponse.getTaskId() != null) {
                // 等待一段时间后查询结果
                Thread.sleep(15000); // 等待15秒
                
                CompletableFuture<TranslateResponse> resultFuture = ghostcutProvider.getBatchTranslateResult(submitResponse.getTaskId());
                TranslateResponse resultResponse = resultFuture.get();
                
                logger.info("鬼手剪辑翻译结果 - 成功: {}, 状态: {}, 结果URL: {}, 错误信息: {}", 
                        resultResponse.isSuccess(), 
                        resultResponse.getCloudApiAsyncStatus(),
                        resultResponse.getTranslateImageSyncResultUrl(), 
                        resultResponse.getErrorMessage());
            }
        } catch (Exception e) {
            logger.error("鬼手剪辑翻译测试失败", e);
        }
    }

    @Test
    public void testUnsupportedOperations() {
        logger.info("开始测试不支持的操作");
        
        // 测试易可图不支持的操作
        CloudTranslateProvider yiketuProvider = getProviderByType(ProviderEnum.YIKETU);
        if (yiketuProvider != null && yiketuProvider.isAvailable()) {
            TranslateRequest request = new TranslateRequest();
            request.setRequestId(UUID.randomUUID().toString());
            request.setText("测试文本");
            request.setSourceLanguage("zh");
            request.setTargetLanguage("en");

            try {
                CompletableFuture<TranslateResponse> future = yiketuProvider.translateTextSync(request);
                TranslateResponse response = future.get();
                
                logger.info("易可图文本翻译结果 - 成功: {}, 错误信息: {}", 
                        response.isSuccess(), response.getErrorMessage());
                
                // 应该返回失败，因为易可图不支持文本翻译
                assert !response.isSuccess() : "易可图应该不支持文本翻译";
            } catch (Exception e) {
                logger.error("易可图文本翻译测试失败", e);
            }
        }

        // 测试鬼手剪辑不支持的操作
        CloudTranslateProvider ghostcutProvider = getProviderByType(ProviderEnum.GHOSTCUT);
        if (ghostcutProvider != null && ghostcutProvider.isAvailable()) {
            TranslateRequest request = new TranslateRequest();
            request.setRequestId(UUID.randomUUID().toString());
            request.setText("测试文本");
            request.setSourceLanguage("zh");
            request.setTargetLanguage("en");

            try {
                CompletableFuture<TranslateResponse> future = ghostcutProvider.translateTextSync(request);
                TranslateResponse response = future.get();
                
                logger.info("鬼手剪辑文本翻译结果 - 成功: {}, 错误信息: {}", 
                        response.isSuccess(), response.getErrorMessage());
                
                // 应该返回失败，因为鬼手剪辑不支持文本翻译
                assert !response.isSuccess() : "鬼手剪辑应该不支持文本翻译";
            } catch (Exception e) {
                logger.error("鬼手剪辑文本翻译测试失败", e);
            }
        }
    }

    private CloudTranslateProvider getProviderByType(ProviderEnum providerType) {
        return translateProviders.stream()
                .filter(provider -> provider.getProviderType() == providerType)
                .findFirst()
                .orElse(null);
    }
}
