package com.cashop.translate.web.validation;

import com.cashop.translate.common.enums.AliyunTranslateFormatType;
import com.cashop.translate.common.enums.AliyunTranslateScene;
import org.springframework.util.StringUtils;

/**
 * 阿里云翻译参数验证器
 * 
 * <AUTHOR>
 */
public class AliyunTranslateParamValidator {

    /**
     * 验证并获取有效的formatType
     * 
     * @param formatType 输入的formatType
     * @return 有效的formatType，如果输入无效则返回默认值
     */
    public static String validateAndGetFormatType(String formatType) {
        if (!StringUtils.hasText(formatType)) {
            return AliyunTranslateFormatType.TEXT.getCode();
        }
        
        if (AliyunTranslateFormatType.isValidCode(formatType)) {
            return formatType;
        }
        
        // 如果输入的值无效，返回默认值
        return AliyunTranslateFormatType.TEXT.getCode();
    }

    /**
     * 验证并获取有效的scene
     * 
     * @param scene 输入的scene
     * @return 有效的scene，如果输入无效则返回默认值
     */
    public static String validateAndGetScene(String scene) {
        if (!StringUtils.hasText(scene)) {
            return AliyunTranslateScene.GENERAL.getCode();
        }
        
        if (AliyunTranslateScene.isValidCode(scene)) {
            return scene;
        }
        
        // 如果输入的值无效，返回默认值
        return AliyunTranslateScene.GENERAL.getCode();
    }

    /**
     * 验证formatType是否有效
     * 
     * @param formatType 要验证的formatType
     * @return 是否有效
     */
    public static boolean isValidFormatType(String formatType) {
        return StringUtils.hasText(formatType) && AliyunTranslateFormatType.isValidCode(formatType);
    }

    /**
     * 验证scene是否有效
     * 
     * @param scene 要验证的scene
     * @return 是否有效
     */
    public static boolean isValidScene(String scene) {
        return StringUtils.hasText(scene) && AliyunTranslateScene.isValidCode(scene);
    }
}
