package com.cashop.translate.common.dto;

import java.util.List;

/**
 * 翻译请求DTO
 * 
 * <AUTHOR>
 */
public class TranslateRequest {

    /**
     * 请求ID（可选，未传时使用UUID）
     */
    private String requestId;

    /**
     * 源语言（可选，支持自动检测）
     */
    private String sourceLanguage;

    /**
     * 目标语言（必填）
     */
    private String targetLanguage;

    /**
     * 图片URL列表（必填）
     */
    private List<String> imageUrls;

    /**
     * 图片Base64编码列表（与imageUrls二选一）
     */
    private List<String> imageBase64List;

    /**
     * 待翻译的文本内容（文本翻译时使用）
     */
    private String text;

    /**
     * 文本格式类型（文本翻译时使用）
     */
    private String formatType;

    /**
     * 翻译场景（文本翻译时使用）
     */
    private String scene;

    /**
     * 回调地址（非必填，不为空时当前翻译请求成功后翻译结果回调该接口）
     */
    private String callback;

    /**
     * 图片翻译场景（图片翻译时使用）
     */
    private String imageScene;

    /**
     * 扩展参数
     */
    private String ext;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getSourceLanguage() {
        return sourceLanguage;
    }

    public void setSourceLanguage(String sourceLanguage) {
        this.sourceLanguage = sourceLanguage;
    }

    public String getTargetLanguage() {
        return targetLanguage;
    }

    public void setTargetLanguage(String targetLanguage) {
        this.targetLanguage = targetLanguage;
    }

    public List<String> getImageUrls() {
        return imageUrls;
    }

    public void setImageUrls(List<String> imageUrls) {
        this.imageUrls = imageUrls;
    }

    public List<String> getImageBase64List() {
        return imageBase64List;
    }

    public void setImageBase64List(List<String> imageBase64List) {
        this.imageBase64List = imageBase64List;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getFormatType() {
        return formatType;
    }

    public void setFormatType(String formatType) {
        this.formatType = formatType;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getImageScene() {
        return imageScene;
    }

    public void setImageScene(String imageScene) {
        this.imageScene = imageScene;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }

    public String getCallback() {
        return callback;
    }

    public void setCallback(String callback) {
        this.callback = callback;
    }
}
