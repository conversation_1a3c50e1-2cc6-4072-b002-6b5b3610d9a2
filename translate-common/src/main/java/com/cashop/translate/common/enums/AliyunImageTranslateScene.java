package com.cashop.translate.common.enums;

/**
 * 阿里云图片翻译场景枚举
 * 
 * <AUTHOR>
 */
public enum AliyunImageTranslateScene {

    /**
     * 通用图片翻译
     */
    GENERAL("general", "通用图片翻译"),

    /**
     * 电商领域图片翻译
     */
    E_COMMERCE("e-commerce", "电商领域图片翻译");

    private final String code;
    private final String description;

    AliyunImageTranslateScene(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取枚举
     */
    public static AliyunImageTranslateScene fromCode(String code) {
        for (AliyunImageTranslateScene scene : values()) {
            if (scene.getCode().equals(code)) {
                return scene;
            }
        }
        return GENERAL; // 默认返回通用场景
    }

    /**
     * 验证code是否有效
     */
    public static boolean isValidCode(String code) {
        for (AliyunImageTranslateScene scene : values()) {
            if (scene.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
