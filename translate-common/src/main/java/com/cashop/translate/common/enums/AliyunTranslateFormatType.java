package com.cashop.translate.common.enums;

/**
 * 阿里云翻译文本格式类型枚举
 * 
 * <AUTHOR>
 */
public enum AliyunTranslateFormatType {

    /**
     * 纯文本格式
     */
    TEXT("text", "纯文本格式"),

    /**
     * HTML格式
     */
    HTML("html", "HTML格式");

    private final String code;
    private final String description;

    AliyunTranslateFormatType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取枚举
     */
    public static AliyunTranslateFormatType fromCode(String code) {
        for (AliyunTranslateFormatType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return TEXT; // 默认返回纯文本格式
    }

    /**
     * 验证code是否有效
     */
    public static boolean isValidCode(String code) {
        for (AliyunTranslateFormatType type : values()) {
            if (type.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
