package com.cashop.translate.common.enums;

/**
 * 阿里云翻译场景枚举
 * 
 * <AUTHOR>
 */
public enum AliyunTranslateScene {

    /**
     * 通用场景
     */
    GENERAL("general", "通用场景"),

    /**
     * 标题场景
     */
    TITLE("title", "标题场景"),

    /**
     * 交流场景
     */
    COMMUNICATION("communication", "交流场景"),

    /**
     * 医疗场景
     */
    MEDICAL("medical", "医疗场景"),

    /**
     * 社交场景
     */
    SOCIAL("social", "社交场景");

    private final String code;
    private final String description;

    AliyunTranslateScene(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取枚举
     */
    public static AliyunTranslateScene fromCode(String code) {
        for (AliyunTranslateScene scene : values()) {
            if (scene.getCode().equals(code)) {
                return scene;
            }
        }
        return GENERAL; // 默认返回通用场景
    }

    /**
     * 验证code是否有效
     */
    public static boolean isValidCode(String code) {
        for (AliyunTranslateScene scene : values()) {
            if (scene.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
