package com.cashop.translate.common.dto;

public class TranslateImageAsyncResult {
    private Integer code;
    private String message;
    private Boolean success;
    private String sourceImageUrl;
    private String finalImageUrl;

    public TranslateImageAsyncResult() {

    }

    public TranslateImageAsyncResult(Integer code, String message, Boolean success, String sourceImageUrl, String finalImageUrl) {
        this.code = code;
        this.message = message;
        this.success = success;
        this.sourceImageUrl = sourceImageUrl;
        this.finalImageUrl = finalImageUrl;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getSourceImageUrl() {
        return sourceImageUrl;
    }

    public void setSourceImageUrl(String sourceImageUrl) {
        this.sourceImageUrl = sourceImageUrl;
    }

    public String getFinalImageUrl() {
        return finalImageUrl;
    }

    public void setFinalImageUrl(String finalImageUrl) {
        this.finalImageUrl = finalImageUrl;
    }

    @Override
    public String toString() {
        return "TranslateImageAsyncResult{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", success=" + success +
                ", sourceImageUrl='" + sourceImageUrl + '\'' +
                ", finalImageUrl='" + finalImageUrl + '\'' +
                '}';
    }



}
