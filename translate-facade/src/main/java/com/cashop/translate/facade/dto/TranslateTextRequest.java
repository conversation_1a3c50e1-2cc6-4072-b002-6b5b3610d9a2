package com.cashop.translate.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;

/**
 * 文本翻译请求DTO
 * 
 * <AUTHOR>
 */
@Schema(description = "文本翻译请求")
public class TranslateTextRequest {

    @Schema(description = "请求ID（可选，未传时使用UUID）", example = "req_123456")
    private String requestId;

    @Schema(description = "源语言（可选，支持自动检测）", example = "en")
    private String sourceLanguage;

    @NotBlank(message = "目标语言不能为空")
    @Schema(description = "目标语言（必填）", example = "zh", required = true)
    private String targetLanguage;

    @NotBlank(message = "待翻译文本不能为空")
    @Schema(description = "待翻译的文本内容（必填）", example = "Hello, world!", required = true)
    private String text;

    @Schema(description = "文本格式类型", example = "text",
            allowableValues = {"text", "html"},
            defaultValue = "text")
    private String formatType = "text";

    @Schema(description = "翻译场景 可选值：通用翻译（general），商品标题（title），商品描述（description），商品沟通（communication），医疗（medical），社交（social)，金融（finance）", example = "general",
            allowableValues = {"general", "title", "communication", "medical", "social"},
            defaultValue = "general")
    private String scene = "general";

    @Schema(description = "扩展参数", example = "{\"format\":\"plain\"}")
    private String ext;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getSourceLanguage() {
        return sourceLanguage;
    }

    public void setSourceLanguage(String sourceLanguage) {
        this.sourceLanguage = sourceLanguage;
    }

    public String getTargetLanguage() {
        return targetLanguage;
    }

    public void setTargetLanguage(String targetLanguage) {
        this.targetLanguage = targetLanguage;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getFormatType() {
        return formatType;
    }

    public void setFormatType(String formatType) {
        this.formatType = formatType;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }
}
