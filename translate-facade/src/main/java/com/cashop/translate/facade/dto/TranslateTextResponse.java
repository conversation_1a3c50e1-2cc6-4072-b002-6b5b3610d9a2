package com.cashop.translate.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 文本翻译响应DTO
 * 
 * <AUTHOR>
 */
@Schema(description = "文本翻译响应")
public class TranslateTextResponse {

    @Schema(description = "请求ID", example = "req_123456")
    private String requestId;

    @Schema(description = "是否成功", example = "true")
    private boolean success;

    @Schema(description = "翻译后的文本", example = "你好，世界！")
    private String translatedText;

    @Schema(description = "源语言", example = "en")
    private String sourceLanguage;

    @Schema(description = "目标语言", example = "zh")
    private String targetLanguage;

    @Schema(description = "原始文本", example = "Hello, world!")
    private String originalText;

    @Schema(description = "错误信息（失败时）")
    private String errorMessage;

    @Schema(description = "处理时间（毫秒）", example = "150")
    private Long processingTime;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getTranslatedText() {
        return translatedText;
    }

    public void setTranslatedText(String translatedText) {
        this.translatedText = translatedText;
    }

    public String getSourceLanguage() {
        return sourceLanguage;
    }

    public void setSourceLanguage(String sourceLanguage) {
        this.sourceLanguage = sourceLanguage;
    }

    public String getTargetLanguage() {
        return targetLanguage;
    }

    public void setTargetLanguage(String targetLanguage) {
        this.targetLanguage = targetLanguage;
    }

    public String getOriginalText() {
        return originalText;
    }

    public void setOriginalText(String originalText) {
        this.originalText = originalText;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }
}
