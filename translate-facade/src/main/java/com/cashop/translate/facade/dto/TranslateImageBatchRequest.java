package com.cashop.translate.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 图片批量翻译请求DTO
 * 
 * <AUTHOR>
 */
@Schema(description = "图片批量翻译请求")
public class TranslateImageBatchRequest {

    @Schema(description = "请求ID（可选，未传时使用UUID）", example = "req_123456")
    private String requestId;

    @Schema(description = "源语言（可选，支持自动检测）", example = "en")
    private String sourceLanguage;

    @NotBlank(message = "目标语言不能为空")
    @Schema(description = "目标语言（必填）", example = "zh", required = true)
    private String targetLanguage;

    @NotEmpty(message = "图片URL列表不能为空")
    @Schema(description = "图片URL列表（与imageBase64List二选一）", required = true)
    private List<String> imageUrls;

    @Schema(description = "图片Base64编码列表（与imageUrls二选一）")
    private List<String> imageBase64List;

    @Schema(description = "图片翻译场景", example = "general", 
            allowableValues = {"general", "e-commerce"},
            defaultValue = "general")
    private String scene = "general";

    @Schema(description = "扩展参数", example = "{\"format\":\"png\"}")
    private String ext;

    @Schema(description = "回调地址（非必填，不为空时当前翻译请求成功后翻译结果回调该接口）", example = "http://xxx.xxx.xxx/xx/x")
    private String callback;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getSourceLanguage() {
        return sourceLanguage;
    }

    public void setSourceLanguage(String sourceLanguage) {
        this.sourceLanguage = sourceLanguage;
    }

    public String getTargetLanguage() {
        return targetLanguage;
    }

    public void setTargetLanguage(String targetLanguage) {
        this.targetLanguage = targetLanguage;
    }

    public List<String> getImageUrls() {
        return imageUrls;
    }

    public void setImageUrls(List<String> imageUrls) {
        this.imageUrls = imageUrls;
    }

    public List<String> getImageBase64List() {
        return imageBase64List;
    }

    public void setImageBase64List(List<String> imageBase64List) {
        this.imageBase64List = imageBase64List;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }

    public String getCallback() {
        return callback;
    }

    public void setCallback(String callback) {
        this.callback = callback;
    }
    
}
