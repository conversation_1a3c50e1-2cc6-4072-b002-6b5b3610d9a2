#!/usr/bin/env python3
"""
MCP 协议常量定义
参考 Go 代码中的 types 包
"""

# JSON-RPC 错误码
PARSE_ERROR = -32700
INVALID_REQUEST = -32600
METHOD_NOT_FOUND = -32601
INVALID_PARAMS = -32602
INTERNAL_ERROR = -32603

# MCP 方法名
MethodInitialize = "initialize"
MethodInitialized = "notifications/initialized"
MethodPing = "ping"
MethodResourcesList = "resources/list"
MethodResourcesTemplatesList = "resources/templates/list"
MethodResourcesRead = "resources/read"
MethodPromptsList = "prompts/list"
MethodPromptsGet = "prompts/get"
MethodToolsList = "tools/list"
MethodToolsCall = "tools/call"
MethodNotificationResourcesListChanged = "notifications/resources/list_changed"
MethodNotificationResourceUpdated = "notifications/resources/updated"
MethodNotificationPromptsListChanged = "notifications/prompts/list_changed"
MethodNotificationToolsListChanged = "notifications/tools/list_changed"

# 服务器信息
SERVER_NAME = "ai-code-mcp-proxy"
SERVER_VERSION = "1.0.0"

# 协议版本
LATEST_PROTOCOL_VERSION = "2024-11-05"
JSONRPC_VERSION = "2.0"

# 客户端信息
CLIENT_NAME = "mcp-proxy-client"
CLIENT_VERSION = "1.0.0"

def create_initialize_request():
    """创建符合MCP规范的初始化请求"""
    return {
        "jsonrpc": JSONRPC_VERSION,
        "id": 1,
        "method": MethodInitialize,
        "params": {
            "protocolVersion": LATEST_PROTOCOL_VERSION,
            "capabilities": {
                "sampling": {}
            },
            "clientInfo": {
                "name": CLIENT_NAME,
                "version": CLIENT_VERSION
            }
        }
    }

def create_initialize_response(request_id):
    """创建符合MCP规范的初始化响应"""
    return {
        "jsonrpc": JSONRPC_VERSION,
        "id": request_id,
        "result": {
            "protocolVersion": LATEST_PROTOCOL_VERSION,
            "capabilities": {
                "tools": {
                    "listChanged": False
                }
            },
            "serverInfo": {
                "name": SERVER_NAME,
                "version": SERVER_VERSION
            }
        }
    }

def create_jsonrpc_error(request_id, code, message, data=None):
    """创建JSON-RPC错误响应"""
    error = {
        "code": code,
        "message": message
    }
    if data is not None:
        error["data"] = data
    
    return {
        "jsonrpc": JSONRPC_VERSION,
        "id": request_id,
        "error": error
    }

def create_jsonrpc_response(request_id, result):
    """创建JSON-RPC成功响应"""
    return {
        "jsonrpc": JSONRPC_VERSION,
        "id": request_id,
        "result": result
    } 