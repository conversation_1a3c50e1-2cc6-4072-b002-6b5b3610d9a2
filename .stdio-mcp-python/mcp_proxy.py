#!/usr/bin/env python3
"""
MCP Proxy - 将内网 SSE MCP 服务转换为 stdio MCP
用于连接 http://zuul.infra.akcstable.com/mcpserver/ai-code-server/sse (SSE MCP 服务)
基于 MCP 2024-11-05 版本规范实现
参考 Go 代码实现模式
"""

import asyncio
import json
import sys
import logging
import time
import signal
from typing import Any, Dict, List, Optional
import aiohttp
import traceback
import uuid
import re
from mcp_constants import (
    create_initialize_request, create_initialize_response, 
    create_jsonrpc_error, create_jsonrpc_response,
    MethodInitialize, INTERNAL_ERROR, LATEST_PROTOCOL_VERSION
)

# 配置日志
import os
script_dir = os.path.dirname(os.path.abspath(__file__))
log_file = os.path.join(script_dir, 'mcp_proxy.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stderr)
    ]
)
logger = logging.getLogger(__name__)

class SSEMCPProxy:
    """SSE MCP 代理服务器，将 stdio MCP 请求转发到内网 SSE MCP 服务"""

    def __init__(self):
        self.base_url = "http://zuul.infra.akcstable.com/mcpserver/ai-code-server"
        self.sse_url = f"{self.base_url}/sse"
        self.session = None
        self.sse_connection = None
        self.pending_requests = {}  # 存储待响应的请求
        self.session_id = None  # 存储从 SSE 获取的 session_id
        self.message_endpoint = None  # 从 SSE 获取的动态消息端点

        # 重连和心跳管理
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 2  # 重连延迟（秒）
        self.heartbeat_timeout = 60  # 心跳超时（秒）
        self.last_heartbeat = None
        self.is_reconnecting = False
        self.reconnect_count = 0
        self.sse_task = None
        self.heartbeat_task = None
        self.initialized = False
        self.sse_ready = asyncio.Event()  # SSE连接就绪事件
        self.session_initialized = False  # 会话是否已初始化
        self.initialization_complete = asyncio.Event()  # 初始化完成事件

    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=60),
            connector=aiohttp.TCPConnector(limit=10)
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        try:
            # 取消 SSE 处理任务
            if self.sse_task:
                self.sse_task.cancel()
                try:
                    await self.sse_task
                except asyncio.CancelledError:
                    pass

            # 关闭 SSE 连接
            if self.sse_connection:
                self.sse_connection.close()

            # 关闭 HTTP 会话
            if self.session:
                await self.session.close()

        except Exception as e:
            logger.error(f"清理资源时异常: {str(e)}")

    async def initialize_sse_connection(self):
        """初始化 SSE 连接 - 参考Go代码实现"""
        try:
            logger.info(f"初始化 SSE 连接: {self.sse_url}")
            
            # 参考Go代码：先建立SSE连接获取session_id
            headers = {
                'Accept': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive'
            }
            
            # 启动SSE监听任务
            self.sse_task = asyncio.create_task(self.listen_sse())

            # 启动心跳监控任务
            self.heartbeat_task = asyncio.create_task(self.monitor_heartbeat())

            # 等待获取session_id
            try:
                await asyncio.wait_for(self.sse_ready.wait(), timeout=10.0)
                logger.info(f"SSE连接就绪，session_id: {self.session_id}")

                # 初始化心跳时间
                self.last_heartbeat = time.time()

                # 发送初始化请求来启动会话
                await self.send_session_initialize()

            except asyncio.TimeoutError:
                logger.warning("等待SSE连接超时，使用默认配置")
                self.session_id = "default_session"
                
        except Exception as e:
            logger.error(f"SSE 连接异常: {str(e)}")
            raise

    async def listen_sse(self):
        """监听 SSE 通知 - 参考Go代码实现"""
        try:
            logger.info("开始监听 SSE 通知...")
            
            async with self.session.get(
                self.sse_url,
                headers={
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive'
                }
            ) as response:
                
                if response.status != 200:
                    logger.error(f"SSE连接失败: HTTP {response.status}")
                    return
                
                self.sse_connection = response
                logger.info("SSE连接建立成功")
                
                buffer = ""
                event = ""
                data = ""
                
                async for chunk in response.content.iter_chunked(1024):
                    # 使用错误处理来避免 UTF-8 解码错误
                    chunk_str = chunk.decode('utf-8', errors='replace')
                    buffer += chunk_str
                    
                    # 按行处理SSE数据
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        line = line.strip()
                        
                        if not line:
                            # 空行表示事件结束
                            if event and data:
                                logger.debug(f"收到SSE通知: event={event}, data={data}")
                                await self.process_sse_message(event, data)
                                event, data = "", ""
                            continue
                        
                        # 处理SSE字段
                        if line.startswith('event:'):
                            event = line[6:].strip()
                        elif line.startswith('data:'):
                            data_content = line[5:].strip()

                            # 检查是否包含session_id的端点信息
                            if '?session_id=' in data_content and data_content.startswith('/mcpserver/ai-code-server/messages'):
                                # 构造完整的消息端点
                                self.message_endpoint = f"http://zuul.infra.akcstable.com{data_content}"

                                # 提取session_id
                                match = re.search(r'session_id=([^&\s]+)', data_content)
                                if match:
                                    self.session_id = match.group(1).rstrip('\r')
                                    logger.info(f"提取到session_id: {self.session_id}")
                                    logger.info(f"设置消息端点: {self.message_endpoint}")
                                    self.sse_ready.set()
                            else:
                                # 普通数据行
                                data = data_content
                        elif line.startswith(': ping') or line.startswith(':ping') or 'ping' in line.lower():
                            # 更新心跳时间
                            self.last_heartbeat = time.time()
                            logger.debug(f"收到心跳: {line.strip()}")

                            # 可选：回应心跳（如果服务端需要）
                            # await self.send_pong()
                        
        except Exception as e:
            logger.error(f"SSE监听异常: {str(e)}")
            logger.error(traceback.format_exc())

            # 如果不是主动重连，则尝试重连
            if not self.is_reconnecting:
                await self.handle_connection_lost()

    async def monitor_heartbeat(self):
        """监控心跳，检测连接状态"""
        logger.info("启动心跳监控")

        while True:
            try:
                await asyncio.sleep(15)  # 每15秒检查一次

                if self.last_heartbeat is None:
                    # 初始化心跳时间
                    self.last_heartbeat = time.time()
                    logger.debug("初始化心跳时间")
                    continue

                # 检查心跳超时
                time_since_heartbeat = time.time() - self.last_heartbeat
                logger.debug(f"距离上次心跳: {time_since_heartbeat:.1f}s")

                if time_since_heartbeat > self.heartbeat_timeout:
                    logger.warning(f"心跳超时 ({time_since_heartbeat:.1f}s > {self.heartbeat_timeout}s)，触发重连")
                    await self.handle_connection_lost()
                    break
                elif time_since_heartbeat > self.heartbeat_timeout * 0.7:
                    logger.warning(f"心跳即将超时 ({time_since_heartbeat:.1f}s)")

            except asyncio.CancelledError:
                logger.info("心跳监控任务被取消")
                break
            except Exception as e:
                logger.error(f"心跳监控异常: {str(e)}")
                await asyncio.sleep(5)  # 异常后等待5秒再继续

    async def handle_connection_lost(self):
        """处理连接丢失，尝试重连"""
        if self.is_reconnecting:
            logger.debug("已在重连中，跳过")
            return

        self.is_reconnecting = True
        logger.info("检测到连接丢失，开始重连...")

        try:
            # 取消现有任务
            if self.sse_task and not self.sse_task.done():
                logger.debug("取消SSE任务")
                self.sse_task.cancel()
                try:
                    await self.sse_task
                except asyncio.CancelledError:
                    pass

            if self.heartbeat_task and not self.heartbeat_task.done():
                logger.debug("取消心跳监控任务")
                self.heartbeat_task.cancel()
                try:
                    await self.heartbeat_task
                except asyncio.CancelledError:
                    pass

            # 重置状态
            self.sse_ready.clear()
            self.session_initialized = False
            self.session_id = None
            self.message_endpoint = None
            self.last_heartbeat = None

            # 尝试重连
            for attempt in range(self.max_reconnect_attempts):
                try:
                    logger.info(f"重连尝试 {attempt + 1}/{self.max_reconnect_attempts}")

                    # 等待一段时间再重连（指数退避）
                    if attempt > 0:
                        delay = min(self.reconnect_delay * (2 ** attempt), 30)  # 最大30秒
                        logger.info(f"等待 {delay} 秒后重连...")
                        await asyncio.sleep(delay)

                    # 重新初始化连接
                    await self.initialize_sse_connection()

                    logger.info("重连成功！")
                    self.reconnect_count = 0
                    return  # 成功重连，退出

                except Exception as e:
                    logger.error(f"重连尝试 {attempt + 1} 失败: {str(e)}")
                    if attempt == self.max_reconnect_attempts - 1:
                        logger.error("达到最大重连次数，放弃重连")
                        raise

        except Exception as e:
            logger.error(f"重连过程中发生异常: {str(e)}")
            raise
        finally:
            self.is_reconnecting = False

    async def close(self):
        """优雅关闭代理"""
        logger.info("正在关闭MCP代理...")

        # 取消所有任务
        if self.sse_task and not self.sse_task.done():
            self.sse_task.cancel()
            try:
                await self.sse_task
            except asyncio.CancelledError:
                pass

        if self.heartbeat_task and not self.heartbeat_task.done():
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass

        # 关闭HTTP会话
        if self.session:
            await self.session.close()

        logger.info("MCP代理已关闭")

    async def send_session_initialize(self):
        """发送会话初始化请求"""
        try:
            logger.info("发送会话初始化请求...")

            # 构造初始化请求 - 符合MCP 2024-11-05规范
            init_request = create_initialize_request()

            # 发送初始化请求
            request_url = self.message_endpoint
            logger.info(f"发送初始化请求到: {request_url}")
            logger.info(f"初始化请求体: {json.dumps(init_request, ensure_ascii=False, indent=2)}")

            async with self.session.post(
                request_url,
                json=init_request,
                headers={'Content-Type': 'application/json'}
            ) as response:
                logger.info(f"初始化响应状态: HTTP {response.status}")
                response_text = await response.text()
                logger.info(f"初始化响应内容: {response_text}")

                if response.status == 202:
                    logger.info("会话初始化请求已接受，等待SSE响应...")
                    self.session_initialized = True
                elif response.status == 200:
                    logger.info("会话初始化成功")
                    self.session_initialized = True
                else:
                    logger.error(f"会话初始化失败: HTTP {response.status}")
                    logger.error(f"错误内容: {response_text}")

        except Exception as e:
            logger.error(f"发送会话初始化请求失败: {str(e)}")
            logger.error(traceback.format_exc())

    async def process_sse_message(self, event: str, data: str):
        """处理SSE消息"""
        try:
            if not data:
                return
                
            # 尝试解析JSON数据
            try:
                message = json.loads(data)
                logger.debug(f"解析SSE JSON消息: {message}")
                
                # 匹配请求ID
                request_id = None
                if 'id' in message:
                    request_id = str(message['id'])
                elif 'jsonrpc' in message and 'id' in message:
                    request_id = str(message['id'])
                
                if request_id and request_id in self.pending_requests:
                    logger.info(f"匹配到请求ID: {request_id}")
                    future = self.pending_requests.pop(request_id)
                    future.set_result(message)

                    # 检查是否是初始化响应
                    if 'result' in message and 'protocolVersion' in message.get('result', {}):
                        logger.info("收到初始化响应，会话初始化完成")
                        self.session_initialized = True
                        self.initialization_complete.set()

                else:
                    logger.debug(f"未匹配的SSE消息: event={event}, data={data}")
                    
            except json.JSONDecodeError:
                logger.debug(f"非JSON SSE数据: event={event}, data={data}")
                
        except Exception as e:
            logger.error(f"处理SSE消息异常: {str(e)}")
    
    async def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理 MCP 请求并转发到 SSE MCP 服务"""
        try:
            # 确保请求符合 JSON-RPC 2.0 格式
            if 'jsonrpc' not in request:
                request['jsonrpc'] = '2.0'

            # 如果没有 ID，生成一个
            if 'id' not in request:
                request['id'] = str(uuid.uuid4())

            request_id = str(request['id'])
            method = request.get('method', '')

            logger.info(f"处理请求: {method} (ID: {request_id})")

            # 如果是initialize请求，需要特殊处理
            if method == 'initialize':
                # 确保 SSE 连接已建立并且会话已初始化
                if not self.session_id or not self.message_endpoint or not self.session_initialized:
                    logger.info("SSE 连接或会话未初始化，正在初始化...")
                    await self.initialize_sse_connection()
                    self.initialized = True

                    # 等待会话初始化完成
                    if not self.session_initialized:
                        logger.warning("会话初始化失败，但继续尝试发送请求")
                
                # 对于initialize请求，直接返回成功响应
                return create_initialize_response(request_id)
            else:
                # 对于非初始化请求，必须等待初始化完成
                if not self.session_initialized:
                    logger.info("等待会话初始化完成...")
                    try:
                        # 等待初始化完成，最多等待30秒
                        await asyncio.wait_for(self.initialization_complete.wait(), timeout=30.0)
                        logger.info("会话初始化完成，继续处理请求")
                    except asyncio.TimeoutError:
                        logger.error("等待会话初始化超时")
                        return create_jsonrpc_error(request_id, INTERNAL_ERROR, "Session initialization timeout")

            # 先注册等待响应，再发送请求
            future = asyncio.Future()
            self.pending_requests[request_id] = future
            logger.debug(f"注册等待请求: {request_id}")

            try:
                # 发送请求到 MCP 服务
                await self.send_request(request)

                # 等待响应
                response = await asyncio.wait_for(future, timeout=30.0)

                # 确保响应符合 JSON-RPC 2.0 格式
                if isinstance(response, dict):
                    if 'jsonrpc' not in response:
                        response['jsonrpc'] = '2.0'
                    if 'id' not in response:
                        response['id'] = request_id

                return response

            except asyncio.TimeoutError:
                self.pending_requests.pop(request_id, None)
                logger.warning(f"请求超时: {request_id}")
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "error": {
                        "code": -32603,
                        "message": "Request timeout"
                    }
                }
            except Exception as e:
                self.pending_requests.pop(request_id, None)
                logger.error(f"等待响应异常: {str(e)}")
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "error": {
                        "code": -32603,
                        "message": f"Response error: {str(e)}"
                    }
                }

        except Exception as e:
            logger.error(f"请求处理失败: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "jsonrpc": "2.0",
                "id": request.get('id'),
                "error": {
                    "code": -32603,
                    "message": f"Internal error: {str(e)}"
                }
            }

    async def send_request(self, request: Dict[str, Any]):
        """发送请求到 MCP 服务 - 使用从 SSE 获取的动态端点"""
        try:
            # 使用从 SSE 获取的消息端点
            if self.message_endpoint:
                request_url = self.message_endpoint
                logger.debug(f"使用 SSE 消息端点: {request_url}")
            else:
                # 如果没有获取到动态端点，使用默认格式
                if not self.session_id:
                    logger.warning("未获取到session_id，使用默认值")
                    self.session_id = "default_session"

                request_url = f"{self.base_url}/messages?session_id={self.session_id}"
                logger.debug(f"使用默认消息端点: {request_url}")

            # 输出详细的请求信息用于调试
            logger.info(f"=== 发送请求详情 ===")
            logger.info(f"请求URL: {request_url}")
            logger.info(f"请求方法: POST")
            logger.info(f"请求头: {{'Content-Type': 'application/json'}}")
            logger.info(f"请求体: {json.dumps(request, ensure_ascii=False, indent=2)}")
            logger.info(f"Session ID: {self.session_id}")
            logger.info(f"Message Endpoint: {self.message_endpoint}")
            logger.info(f"==================")

            # 发送POST请求
            async with self.session.post(
                request_url,
                json=request,
                headers={'Content-Type': 'application/json'}
            ) as response:

                # 输出详细的响应信息用于调试
                logger.info(f"=== 响应详情 ===")
                logger.info(f"响应状态码: HTTP {response.status}")
                logger.info(f"响应头: {dict(response.headers)}")

                response_text = await response.text()
                logger.info(f"响应体: {response_text}")
                logger.info(f"===============")

                if response.status == 200 or response.status == 202:
                    logger.info(f"请求发送成功: HTTP {response.status}")

                    # 对于202状态码，响应通过SSE流返回，不需要立即处理
                    if response.status == 202:
                        logger.info("请求已接受，等待SSE响应")
                        return

                    # 对于200状态码，尝试读取响应
                    try:
                        response_data = json.loads(response_text)
                        logger.info(f"解析响应成功: {response_data}")

                        # 如果响应包含结果，直接返回
                        if 'result' in response_data or 'error' in response_data:
                            request_id = str(request.get('id', ''))
                            if request_id in self.pending_requests:
                                future = self.pending_requests.pop(request_id)
                                future.set_result(response_data)
                    except Exception as e:
                        logger.error(f"解析响应失败: {str(e)}")
                        # 响应可能通过SSE流返回

                else:
                    logger.error(f"请求失败: HTTP {response.status}")
                    logger.error(f"错误响应内容: {response_text}")

                    # 尝试解析错误响应
                    try:
                        error_data = json.loads(response_text)
                        logger.error(f"错误详情: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
                    except:
                        logger.error(f"无法解析错误响应为JSON")

                    # 检查是否是认证或权限问题
                    if response.status == 401:
                        logger.error("认证失败 - 可能需要检查session_id")
                    elif response.status == 403:
                        logger.error("权限不足 - 可能需要检查访问权限")
                    elif response.status == 400:
                        logger.error("请求格式错误 - 请检查请求参数格式")
                    elif response.status == 404:
                        logger.error("端点不存在 - 请检查URL路径")
                    elif response.status == 500:
                        logger.error("服务器内部错误 - 请检查服务器状态")

        except Exception as e:
            logger.error(f"发送请求失败: {str(e)}")
            raise

async def handle_stdio():
    """处理 stdio 通信"""
    async with SSEMCPProxy() as proxy:
        logger.info("SSE MCP Proxy 启动成功")

        # 启动时立即建立 SSE 连接和初始化会话
        try:
            logger.info("启动时建立 SSE 连接...")
            await proxy.initialize_sse_connection()
            proxy.initialized = True
            logger.info("SSE 连接和会话初始化完成")
        except Exception as e:
            logger.error(f"启动时初始化失败: {str(e)}")
            # 继续运行，但会话可能未初始化

        try:
            while True:
                # 读取请求
                line = await asyncio.get_event_loop().run_in_executor(
                    None, sys.stdin.readline
                )

                if not line:
                    break

                line = line.strip()
                if not line:
                    continue

                try:
                    request = json.loads(line)
                    logger.debug(f"收到请求: {request}")

                    # 处理请求
                    response = await proxy.handle_request(request)

                    # 发送响应
                    response_line = json.dumps(response, ensure_ascii=False)
                    print(response_line, flush=True)
                    logger.debug(f"发送响应: {response}")

                except json.JSONDecodeError as e:
                    logger.error(f"JSON 解析失败: {str(e)}")
                    error_response = {
                        "error": {
                            "code": -32700,
                            "message": "Parse error"
                        }
                    }
                    print(json.dumps(error_response), flush=True)

        except KeyboardInterrupt:
            logger.info("收到中断信号，正在关闭...")
        except Exception as e:
            logger.error(f"stdio 处理异常: {str(e)}")
            logger.error(traceback.format_exc())


def main():
    """主函数"""
    try:
        asyncio.run(handle_stdio())
    except KeyboardInterrupt:
        logger.info("收到键盘中断，正在关闭...")
    except Exception as e:
        logger.error(f"程序异常退出: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
